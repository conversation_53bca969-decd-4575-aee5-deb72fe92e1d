<?php

namespace App\Console\Commands;

use App\Services\AI\LLM\OllamaService;
use App\Services\AI\Embedding\Providers\OllamaEmbeddingProvider;
use Illuminate\Console\Command;

class TestOllamaService extends Command
{
    protected $signature = 'ai:test-ollama {--pull-models : Pull required models if not available}';
    protected $description = 'Test Ollama service and models';

    private OllamaService $ollamaService;
    private OllamaEmbeddingProvider $embeddingProvider;

    public function __construct(OllamaService $ollamaService)
    {
        parent::__construct();
        $this->ollamaService = $ollamaService;
        $this->embeddingProvider = new OllamaEmbeddingProvider();
    }

    public function handle(): int
    {
        $this->info('🦙 Testing Ollama Service');

        // Check if Ollama is running
        $this->info('🔍 Checking Ollama availability...');
        if (!$this->ollamaService->validateConfiguration()) {
            $this->error('❌ Ollama is not available');
            $this->line('   Make sure Ollama is installed and running:');
            $this->line('   - Install: https://ollama.ai/');
            $this->line('   - Start: ollama serve');
            return Command::FAILURE;
        }

        $this->info('✅ Ollama is running');

        // Get available models
        $this->info('📋 Checking available models...');
        $availableModels = $this->ollamaService->getAvailableModels();
        
        if (empty($availableModels)) {
            $this->warn('⚠️ No models found');
        } else {
            $this->info('Available models:');
            foreach ($availableModels as $model) {
                $this->line("   - {$model}");
            }
        }

        // Check required models
        $requiredModels = [
            config('ai_assistant.ollama.embedding_model', 'nomic-embed-text'),
            config('ai_assistant.ollama.chat_model', 'llama3.2'),
        ];

        $missingModels = [];
        foreach ($requiredModels as $model) {
            $isAvailable = collect($availableModels)->contains(fn($available) => 
                $available === $model || str_starts_with($available, $model)
            );
            
            if ($isAvailable) {
                $this->info("✅ {$model} is available");
            } else {
                $this->warn("⚠️ {$model} is not available");
                $missingModels[] = $model;
            }
        }

        // Pull missing models if requested
        if (!empty($missingModels) && $this->option('pull-models')) {
            $this->info('📥 Pulling missing models...');
            foreach ($missingModels as $model) {
                $this->line("Pulling {$model}...");
                $result = $this->ollamaService->pullModel($model);
                
                if ($result['success']) {
                    $this->info("✅ Successfully pulled {$model}");
                } else {
                    $this->error("❌ Failed to pull {$model}: " . $result['error']);
                }
            }
        } elseif (!empty($missingModels)) {
            $this->warn('Missing models detected. Run with --pull-models to download them:');
            foreach ($missingModels as $model) {
                $this->line("   ollama pull {$model}");
            }
        }

        // Test embedding generation
        if ($this->embeddingProvider->isEmbeddingModelAvailable()) {
            $this->info('🔤 Testing embedding generation...');
            $testText = "This is a test for eBay-Shopify integration embeddings.";
            
            $result = $this->embeddingProvider->generateEmbedding($testText);
            
            if ($result['success']) {
                $this->info('✅ Embedding generation successful');
                $this->line('   - Dimension: ' . count($result['embedding']));
                $this->line('   - Provider dimension: ' . $this->embeddingProvider->getEmbeddingDimension());
                $this->line('   - Tokens used: ' . $result['usage']['total_tokens']);
            } else {
                $this->error('❌ Embedding generation failed: ' . $result['error']);
            }
        } else {
            $this->warn('⚠️ Embedding model not available, skipping embedding test');
        }

        // Test chat completion
        $chatModel = config('ai_assistant.ollama.chat_model', 'llama3.2');
        $hasChatModel = collect($availableModels)->contains(fn($model) => 
            $model === $chatModel || str_starts_with($model, $chatModel)
        );

        if ($hasChatModel) {
            $this->info('💬 Testing chat completion...');
            $messages = [
                ['role' => 'system', 'content' => 'You are a helpful assistant for eBay-Shopify integration.'],
                ['role' => 'user', 'content' => 'What is inventory synchronization?']
            ];
            
            $result = $this->ollamaService->generateChatCompletion($messages);
            
            if ($result['success']) {
                $this->info('✅ Chat completion successful');
                $this->line('   - Response length: ' . strlen($result['content']));
                $this->line('   - Tokens used: ' . $result['usage']['total_tokens']);
                $this->line('   - Model: ' . $result['model']);
                $this->line('   - Preview: ' . substr($result['content'], 0, 100) . '...');
            } else {
                $this->error('❌ Chat completion failed: ' . $result['error']);
            }
        } else {
            $this->warn('⚠️ Chat model not available, skipping chat test');
        }

        // Configuration summary
        $this->info('⚙️ Configuration Summary:');
        $this->line('   - Base URL: ' . config('ai_assistant.ollama.base_url'));
        $this->line('   - Embedding Model: ' . config('ai_assistant.ollama.embedding_model'));
        $this->line('   - Chat Model: ' . config('ai_assistant.ollama.chat_model'));
        $this->line('   - Embedding Dimension: ' . config('ai_assistant.ollama.dimension'));
        $this->line('   - Current Provider: ' . config('ai_assistant.embedding_provider'));

        $this->info('🎉 Ollama testing completed!');
        return Command::SUCCESS;
    }
}
