<?php

namespace App\Console\Commands;

use App\Services\AI\LLM\OpenAIService;
use Illuminate\Console\Command;

class TestAIServices extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'ai:test-services';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test AI services (OpenAI, Pinecone)';

    private OpenAIService $openAIService;

    public function __construct(OpenAIService $openAIService)
    {
        parent::__construct();
        $this->openAIService = $openAIService;
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('🧪 Testing AI Services');

        // Test OpenAI Configuration
        $this->info('📝 Testing OpenAI Configuration...');
        if ($this->openAIService->validateConfiguration()) {
            $this->info('✅ OpenAI configuration is valid');
        } else {
            $this->error('❌ OpenAI configuration is invalid');
            return Command::FAILURE;
        }

        // Test OpenAI Embedding
        $this->info('🔤 Testing OpenAI Embedding Generation...');
        $testText = "This is a test document for eBay-Shopify integration.";
        $embeddingResult = $this->openAIService->generateEmbedding($testText);

        if ($embeddingResult['success']) {
            $this->info('✅ OpenAI embedding generation successful');
            $this->line('   - Embedding dimension: ' . count($embeddingResult['embedding']));
            $this->line('   - Tokens used: ' . $embeddingResult['usage']['total_tokens']);
        } else {
            $this->warn('⚠️ OpenAI embedding generation failed: ' . $embeddingResult['error']);
            $this->line('   - Continuing without embedding functionality for now');
        }

        // Test OpenAI Chat
        $this->info('💬 Testing OpenAI Chat Completion...');
        $messages = [
            ['role' => 'system', 'content' => 'You are a helpful assistant for eBay-Shopify integration.'],
            ['role' => 'user', 'content' => 'What is eBay category selection?']
        ];

        $chatResult = $this->openAIService->chat($messages);

        if ($chatResult['success']) {
            $this->info('✅ OpenAI chat completion successful');
            $this->line('   - Response length: ' . strlen($chatResult['content']));
            $this->line('   - Tokens used: ' . $chatResult['usage']['total_tokens']);
            $this->line('   - Response preview: ' . substr($chatResult['content'], 0, 100) . '...');
        } else {
            $this->error('❌ OpenAI chat completion failed: ' . $chatResult['error']);
            return Command::FAILURE;
        }

        $this->info('🎉 All AI services are working correctly!');
        return Command::SUCCESS;
    }
}
