<?php

namespace App\Console\Commands;

use App\Services\AI\LLM\OpenAIService;
use App\Services\AI\LLM\OllamaService;
use App\Services\AI\VectorStore\PineconeService;
use App\Services\AI\Embedding\EmbeddingProviderFactory;
use Illuminate\Console\Command;

class TestAIServices extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'ai:test-services';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test AI services (OpenAI, Pinecone)';

    private OpenAIService $openAIService;
    private OllamaService $ollamaService;
    private PineconeService $pineconeService;

    public function __construct(
        OpenAIService $openAIService,
        OllamaService $ollamaService,
        PineconeService $pineconeService
    ) {
        parent::__construct();
        $this->openAIService = $openAIService;
        $this->ollamaService = $ollamaService;
        $this->pineconeService = $pineconeService;
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('🧪 Testing AI Services');

        // Test OpenAI Configuration
        $this->info('📝 Testing OpenAI Configuration...');
        if ($this->openAIService->validateConfiguration()) {
            $this->info('✅ OpenAI configuration is valid');
        } else {
            $this->error('❌ OpenAI configuration is invalid');
            return Command::FAILURE;
        }

        // Test OpenAI Embedding
        $this->info('🔤 Testing OpenAI Embedding Generation...');
        $testText = "This is a test document for eBay-Shopify integration.";
        $embeddingResult = $this->openAIService->generateEmbedding($testText);

        if ($embeddingResult['success']) {
            $this->info('✅ OpenAI embedding generation successful');
            $this->line('   - Embedding dimension: ' . count($embeddingResult['embedding']));
            $this->line('   - Tokens used: ' . $embeddingResult['usage']['total_tokens']);
        } else {
            $this->warn('⚠️ OpenAI embedding generation failed: ' . $embeddingResult['error']);
            $this->line('   - Continuing without embedding functionality for now');
        }

        // Test OpenAI Chat
        $this->info('💬 Testing OpenAI Chat Completion...');
        $messages = [
            ['role' => 'system', 'content' => 'You are a helpful assistant for eBay-Shopify integration.'],
            ['role' => 'user', 'content' => 'What is eBay category selection?']
        ];

        $chatResult = $this->openAIService->chat($messages);

        if ($chatResult['success']) {
            $this->info('✅ OpenAI chat completion successful');
            $this->line('   - Response length: ' . strlen($chatResult['content']));
            $this->line('   - Tokens used: ' . $chatResult['usage']['total_tokens']);
            $this->line('   - Response preview: ' . substr($chatResult['content'], 0, 100) . '...');
        } else {
            $this->error('❌ OpenAI chat completion failed: ' . $chatResult['error']);
            return Command::FAILURE;
        }

        // Test Ollama Configuration
        $this->info('🦙 Testing Ollama Configuration...');
        if ($this->ollamaService->validateConfiguration()) {
            $this->info('✅ Ollama configuration is valid');

            // Test Ollama Embedding
            $this->info('🔤 Testing Ollama Embedding Generation...');
            $ollamaEmbeddingResult = $this->ollamaService->generateEmbedding($testText);

            if ($ollamaEmbeddingResult['success']) {
                $this->info('✅ Ollama embedding generation successful');
                $this->line('   - Embedding dimension: ' . count($ollamaEmbeddingResult['embedding']));
                $this->line('   - Tokens used: ' . $ollamaEmbeddingResult['usage']['total_tokens']);
            } else {
                $this->warn('⚠️ Ollama embedding generation failed: ' . $ollamaEmbeddingResult['error']);
            }

            // Test Ollama Chat
            $this->info('💬 Testing Ollama Chat Completion...');
            $ollamaChatResult = $this->ollamaService->generateChatCompletion($messages);

            if ($ollamaChatResult['success']) {
                $this->info('✅ Ollama chat completion successful');
                $this->line('   - Response length: ' . strlen($ollamaChatResult['content']));
                $this->line('   - Tokens used: ' . $ollamaChatResult['usage']['total_tokens']);
                $this->line('   - Response preview: ' . substr($ollamaChatResult['content'], 0, 100) . '...');
            } else {
                $this->warn('⚠️ Ollama chat completion failed: ' . $ollamaChatResult['error']);
            }
        } else {
            $this->warn('⚠️ Ollama is not available or not configured');
            $this->line('   - Make sure Ollama is running and required models are installed');
        }

        // Test Embedding Provider Factory
        $this->info('🏭 Testing Embedding Provider Factory...');
        $availableProviders = EmbeddingProviderFactory::getConfiguredProviders();
        $this->line('   - Available providers: ' . implode(', ', $availableProviders));

        $bestProvider = EmbeddingProviderFactory::getBestAvailableProvider();
        if ($bestProvider) {
            $this->info("   - Best available provider: {$bestProvider}");

            $provider = EmbeddingProviderFactory::create($bestProvider);
            $this->line("   - Provider dimension: " . $provider->getEmbeddingDimension());
        } else {
            $this->warn('   - No embedding providers available');
        }

        // Test Pinecone Configuration
        $this->info('🌲 Testing Pinecone Configuration...');
        if ($this->pineconeService->validateConfiguration()) {
            $this->info('✅ Pinecone configuration is valid');
        } else {
            $this->error('❌ Pinecone configuration is invalid');
            return Command::FAILURE;
        }

        // Test Pinecone Vector Operations (if embedding was successful)
        if ($embeddingResult['success']) {
            $this->info('📊 Testing Pinecone Vector Operations...');

            // Test vector upsert
            $testVector = $this->pineconeService->formatVector(
                'test_' . time(),
                $embeddingResult['embedding'],
                [
                    'text' => $testText,
                    'type' => 'test',
                    'timestamp' => time()
                ]
            );

            if ($this->pineconeService->upsertVectors([$testVector])) {
                $this->info('✅ Pinecone vector upsert successful');

                // Test vector search
                $searchResult = $this->pineconeService->search(
                    $embeddingResult['embedding'],
                    5,
                    ['type' => 'test']
                );

                if ($searchResult['success']) {
                    $this->info('✅ Pinecone vector search successful');
                    $this->line('   - Found ' . count($searchResult['matches']) . ' matches');
                    if (!empty($searchResult['matches'])) {
                        $this->line('   - Top match score: ' . round($searchResult['matches'][0]['score'], 4));
                    }
                } else {
                    $this->warn('⚠️ Pinecone vector search failed: ' . $searchResult['error']);
                }
            } else {
                $this->warn('⚠️ Pinecone vector upsert failed');
            }
        } else {
            $this->line('⏭️ Skipping Pinecone vector operations (embedding failed)');
        }

        $this->info('🎉 All AI services are working correctly!');
        return Command::SUCCESS;
    }
}
