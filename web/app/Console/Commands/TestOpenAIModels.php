<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;

class TestOpenAIModels extends Command
{
    protected $signature = 'ai:test-models';
    protected $description = 'Test available OpenAI models';

    public function handle(): int
    {
        $this->info('🔍 Testing Available OpenAI Models');

        $apiKey = config('ai_assistant.openai.api_key');
    
        
        if (empty($apiKey)) {
            $this->error('❌ OpenAI API key not configured');
            return Command::FAILURE;
        }

        // Test different embedding models
        $embeddingModels = [
            'text-embedding-ada-002',
            'text-embedding-3-small',
            'text-embedding-3-large',
        ];

        $this->info('🧪 Testing embedding models...');
        
        foreach ($embeddingModels as $model) {
            $this->line("Testing: {$model}");
            
            try {
                $response = Http::withHeaders([
                    'Authorization' => 'Bearer ' . $apiKey,
                    'Content-Type' => 'application/json',
                ])->timeout(30)->post('https://api.openai.com/v1/embeddings', [
                    'model' => $model,
                    'input' => 'test',
                ]);

               // dd($response->json());

                if ($response->successful()) {
                    $this->info("  ✅ {$model} - Available");
                    $data = $response->json();
                    $this->line("     Dimension: " . count($data['data'][0]['embedding']));
                    
                    // Update .env with working model
                    $this->updateEnvModel($model);
                    break;
                } else {
                    $error = $response->json();
                    $this->warn("  ❌ {$model} - " . ($error['error']['message'] ?? 'Failed'));
                }
            } catch (\Exception $e) {
                $this->warn("  ❌ {$model} - Exception: " . $e->getMessage());
            }
        }

        return Command::SUCCESS;
    }

    private function updateEnvModel(string $model): void
    {
        $envPath = base_path('.env');
        if (file_exists($envPath)) {
            $envContent = file_get_contents($envPath);
            $envContent = preg_replace(
                '/OPENAI_EMBEDDING_MODEL=.*/',
                "OPENAI_EMBEDDING_MODEL={$model}",
                $envContent
            );
            file_put_contents($envPath, $envContent);
            $this->info("  📝 Updated .env with working model: {$model}");
        }
    }
}
