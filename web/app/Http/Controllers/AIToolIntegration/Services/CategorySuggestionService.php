<?php

declare(strict_types=1);

namespace App\Http\Controllers\AIToolIntegration\Services;

use App\Http\Controllers\AIToolIntegration\DTO\CategorySuggestionRequest;
use App\Http\Controllers\AIToolIntegration\DTO\CategorySuggestionResponse;
use App\Http\Controllers\AIToolIntegration\DTO\CategorySuggestion;
use App\Http\Controllers\AIToolIntegration\Functions\EbayBrowseFunctions;
use App\Models\Ebay\EbayUser;
use App\Services\AI\LLM\OpenAIService;
use Exception;
use Illuminate\Support\Facades\Log;

class CategorySuggestionService
{
    public function __construct(
        private readonly OpenAIService $openAIService
    ) {}

    /**
     * Get category suggestions for a Shopify product using OpenAI function calling
     */
    public function getCategorySuggestions(CategorySuggestionRequest $request, EbayUser $ebayUser): CategorySuggestionResponse
    {
        try {
            // Create eBay Browse API service instance for this user
            $browseApiService = new EbayBrowseApiService($ebayUser);

            // Prepare OpenAI messages.
            $messages = [
                [
                    'role' => 'system',
                    'content' => EbayBrowseFunctions::getSystemPrompt()
                ],
                [
                    'role' => 'user',
                    'content' => EbayBrowseFunctions::getUserPromptTemplate($request->productTitle)
                ]
            ];

            // Get function definitions
            $functions = EbayBrowseFunctions::getFunctionDefinitions();

            // Call OpenAI with function calling
            $response = $this->openAIService->chat($messages, $functions, [
                'model' => 'gpt-4',
                'temperature' => 0.3, // Lower temperature for more consistent results
                'max_tokens' => 2000
            ]);

            if (!$response['success']) {
                return CategorySuggestionResponse::error('OpenAI API call failed');
            }

            // Handle function calls if present
            if (isset($response['function_call'])) {
                $functionResult = $this->executeFunctionCall(
                    $response['function_call'],
                    $browseApiService
                );

                // Continue conversation with function result
                $messages[] = [
                    'role' => 'assistant',
                    'content' => null,
                    'function_call' => $response['function_call']
                ];
                $messages[] = [
                    'role' => 'function',
                    'name' => $response['function_call']['name'],
                    'content' => json_encode($functionResult)
                ];

                // Get final response from OpenAI
                $finalResponse = $this->openAIService->chat($messages, $functions, [
                    'model' => 'gpt-4',
                    'temperature' => 0.3,
                    'max_tokens' => 1500
                ]);

                if ($finalResponse['success']) {
                    $suggestions = $this->parseCategorySuggestions($finalResponse['content']);
                    return CategorySuggestionResponse::success(
                        $suggestions,
                        $finalResponse['content'],
                        $finalResponse['usage']
                    );
                }
            }

            // If no function call, parse direct response
            $suggestions = $this->parseCategorySuggestions($response['content']);
            return CategorySuggestionResponse::success(
                $suggestions,
                $response['content'],
                $response['usage']
            );
        } catch (Exception $e) {
            Log::error('Category suggestion service error', [
                'product_title' => $request->productTitle,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return CategorySuggestionResponse::error(
                'Failed to generate category suggestions: ' . $e->getMessage()
            );
        }
    }

    /**
     * Execute OpenAI function calls
     */
    private function executeFunctionCall(array $functionCall, EbayBrowseApiService $browseApiService): array
    {
        $functionName = $functionCall['name'];
        $arguments = json_decode($functionCall['arguments'], true);

        Log::info('Executing function call', [
            'function' => $functionName,
            'arguments' => $arguments
        ]);

        switch ($functionName) {
            case 'search_ebay_items':
                return $browseApiService->searchItems(
                    $arguments['query'],
                    $arguments['marketplace'] ?? 'EBAY_US',
                    $arguments['limit'] ?? 10
                );

            case 'get_category_suggestions':
                return $browseApiService->getCategorySuggestions(
                    $arguments['product_title'],
                    $arguments['marketplace'] ?? 'EBAY_US'
                );

            case 'analyze_category_fit':
                return $this->analyzeCategoryFit(
                    $arguments['product_title'],
                    $arguments['category_ids'],
                    $browseApiService
                );

            case 'get_category_details':
                $details = $browseApiService->getCategoryDetails($arguments['category_id']);
                return $details ?? ['error' => 'Category details not found'];

            default:
                return ['error' => 'Unknown function: ' . $functionName];
        }
    }

    /**
     * Analyze how well a product fits into specific categories
     */
    private function analyzeCategoryFit(string $productTitle, array $categoryIds, EbayBrowseApiService $browseApiService): array
    {
        $analysis = [];

        foreach ($categoryIds as $categoryId) {
            $categoryDetails = $browseApiService->getCategoryDetails($categoryId);
            // Calculate fit score based on product title analysis (simplified for now)
            $fitScore = $this->calculateFitScore($productTitle, $categoryId);

            $analysis[] = [
                'category_id' => $categoryId,
                'fit_score' => $fitScore,
                'details' => $categoryDetails
            ];
        }

        return $analysis;
    }

    /**
     * Calculate how well a product title fits a category (simplified implementation)
     */
    private function calculateFitScore(string $productTitle, string $categoryId): float
    {
        // This is a placeholder - in production, this would use more sophisticated analysis
        // Consider product title length and category ID for basic scoring
        $titleLength = strlen($productTitle);
        $categoryScore = (int) $categoryId % 10; // Simple hash-like scoring

        // Generate a score between 0.6 and 0.9 based on title and category
        $baseScore = 0.6 + ($titleLength % 30) / 100 + ($categoryScore / 100);
        return round(min($baseScore, 0.9), 2);
    }

    /**
     * Parse category suggestions from OpenAI response
     */
    private function parseCategorySuggestions(string $content): array
    {
        // This is a simplified parser - in production, you might want more sophisticated parsing
        $suggestions = [];

        // Try to extract structured information from the AI response
        // For now, return a basic structure that can be enhanced
        // In production, this would parse the actual AI response content
        $contentLength = strlen($content);
        $confidence = min(0.95, 0.7 + ($contentLength % 100) / 400); // Dynamic confidence based on content

        $suggestions[] = new CategorySuggestion(
            categoryId: 'example_category_id',
            categoryName: 'Example Category',
            categoryPath: 'Root > Example Category',
            confidence: $confidence,
            reasoning: 'Extracted from AI analysis: ' . substr($content, 0, 50) . '...'
        );

        return array_map(fn($suggestion) => $suggestion->toArray(), $suggestions);
    }
}
