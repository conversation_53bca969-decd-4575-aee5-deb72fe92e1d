<?php

namespace App\Services\AI\Contracts;

interface EmbeddingProviderInterface
{
    /**
     * Generate embeddings for multiple texts.
     *
     * @param array $texts Array of text strings to embed
     * @return array Response with success status, embeddings, and usage info
     */
    public function generateEmbeddings(array $texts): array;

    /**
     * Generate embedding for a single text.
     *
     * @param string $text Text string to embed
     * @return array Response with success status, embedding, and usage info
     */
    public function generateEmbedding(string $text): array;

    /**
     * Validate that the embedding provider is properly configured and available.
     *
     * @return bool True if provider is available and configured
     */
    public function validateConfiguration(): bool;

    /**
     * Get the dimension size of embeddings produced by this provider.
     *
     * @return int Embedding dimension size
     */
    public function getEmbeddingDimension(): int;

    /**
     * Get the name/identifier of this embedding provider.
     *
     * @return string Provider name
     */
    public function getProviderName(): string;
}
