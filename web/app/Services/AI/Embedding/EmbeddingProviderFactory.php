<?php

namespace App\Services\AI\Embedding;

use App\Services\AI\Contracts\EmbeddingProviderInterface;
use App\Services\AI\Embedding\Providers\OpenAIEmbeddingProvider;
use App\Services\AI\Embedding\Providers\OllamaEmbeddingProvider;
use InvalidArgumentException;

class EmbeddingProviderFactory
{
    /**
     * Create an embedding provider instance based on configuration.
     *
     * @param string|null $provider Provider name ('openai', 'ollama') or null for default
     * @return EmbeddingProviderInterface
     * @throws InvalidArgumentException
     */
    public static function create(?string $provider = null): EmbeddingProviderInterface
    {
        $provider = $provider ?? config('ai_assistant.embedding_provider', 'openai');

        return match (strtolower($provider)) {
            'openai' => new OpenAIEmbeddingProvider(),
            'ollama' => new OllamaEmbeddingProvider(),
            default => throw new InvalidArgumentException("Unsupported embedding provider: {$provider}")
        };
    }

    /**
     * Get all available embedding providers.
     *
     * @return array
     */
    public static function getAvailableProviders(): array
    {
        return [
            'openai' => [
                'name' => 'OpenAI',
                'description' => 'OpenAI embedding models (text-embedding-ada-002, text-embedding-3-small, etc.)',
                'requires_api_key' => true,
                'local' => false,
            ],
            'ollama' => [
                'name' => 'Ollama',
                'description' => 'Local Ollama embedding models (nomic-embed-text, etc.)',
                'requires_api_key' => false,
                'local' => true,
            ],
        ];
    }

    /**
     * Check which providers are currently available/configured.
     *
     * @return array
     */
    public static function getConfiguredProviders(): array
    {
        $configured = [];

        try {
            $openai = new OpenAIEmbeddingProvider();
            if ($openai->validateConfiguration()) {
                $configured[] = 'openai';
            }
        } catch (\Exception $e) {
            // OpenAI not available
        }

        try {
            $ollama = new OllamaEmbeddingProvider();
            if ($ollama->validateConfiguration()) {
                $configured[] = 'ollama';
            }
        } catch (\Exception $e) {
            // Ollama not available
        }

        return $configured;
    }

    /**
     * Get the best available provider based on configuration and availability.
     *
     * @return string|null
     */
    public static function getBestAvailableProvider(): ?string
    {
        $configured = self::getConfiguredProviders();
        
        if (empty($configured)) {
            return null;
        }

        // Prefer the configured provider if it's available
        $preferred = config('ai_assistant.embedding_provider', 'openai');
        if (in_array($preferred, $configured)) {
            return $preferred;
        }

        // Otherwise return the first available
        return $configured[0];
    }
}
