<?php

namespace App\Services\AI\Embedding\Providers;

use App\Services\AI\Contracts\EmbeddingProviderInterface;
use App\Services\AI\LLM\OpenAIService;

class OpenAIEmbeddingProvider implements EmbeddingProviderInterface
{
    private OpenAIService $openAIService;
    private array $config;

    public function __construct()
    {
        $this->openAIService = new OpenAIService();
        $this->config = config('ai_assistant.openai');
    }

    /**
     * Generate embeddings for multiple texts.
     */
    public function generateEmbeddings(array $texts): array
    {
        return $this->openAIService->generateBatchEmbeddings($texts);
    }

    /**
     * Generate embedding for a single text.
     */
    public function generateEmbedding(string $text): array
    {
        return $this->openAIService->generateEmbedding($text);
    }

    /**
     * Validate that OpenAI is properly configured and available.
     */
    public function validateConfiguration(): bool
    {
        return $this->openAIService->validateConfiguration();
    }

    /**
     * Get the dimension size of OpenAI embeddings.
     */
    public function getEmbeddingDimension(): int
    {
        $model = $this->config['embedding_model'] ?? 'text-embedding-ada-002';
        
        return match ($model) {
            'text-embedding-ada-002' => 1536,
            'text-embedding-3-small' => 1536,
            'text-embedding-3-large' => 3072,
            default => 1536, // Default fallback
        };
    }

    /**
     * Get the provider name.
     */
    public function getProviderName(): string
    {
        return 'openai';
    }

    /**
     * Check if embeddings are available (not blocked by API access).
     */
    public function isEmbeddingAvailable(): bool
    {
        // Try a simple embedding test
        $result = $this->generateEmbedding('test');
        return $result['success'] ?? false;
    }
}
