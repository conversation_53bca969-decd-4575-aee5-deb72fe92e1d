<?php

namespace App\Services\AI\Embedding\Providers;

use App\Services\AI\Contracts\EmbeddingProviderInterface;
use App\Services\AI\LLM\OllamaService;

class OllamaEmbeddingProvider implements EmbeddingProviderInterface
{
    private OllamaService $ollamaService;
    private array $config;

    public function __construct()
    {
        $this->ollamaService = new OllamaService();
        $this->config = config('ai_assistant.ollama');
    }

    /**
     * Generate embeddings for multiple texts.
     */
    public function generateEmbeddings(array $texts): array
    {
        return $this->ollamaService->generateEmbeddings($texts);
    }

    /**
     * Generate embedding for a single text.
     */
    public function generateEmbedding(string $text): array
    {
        return $this->ollamaService->generateEmbedding($text);
    }

    /**
     * Validate that Ollama is properly configured and available.
     */
    public function validateConfiguration(): bool
    {
        return $this->ollamaService->validateConfiguration();
    }

    /**
     * Get the dimension size of Ollama embeddings.
     */
    public function getEmbeddingDimension(): int
    {
        $model = $this->config['embedding_model'] ?? 'nomic-embed-text';
        
        return match ($model) {
            'nomic-embed-text' => 768,
            'all-minilm' => 384,
            'sentence-transformers/all-MiniLM-L6-v2' => 384,
            default => (int) ($this->config['dimension'] ?? 768),
        };
    }

    /**
     * Get the provider name.
     */
    public function getProviderName(): string
    {
        return 'ollama';
    }

    /**
     * Get available models from Ollama.
     */
    public function getAvailableModels(): array
    {
        return $this->ollamaService->getAvailableModels();
    }

    /**
     * Pull a model from Ollama registry.
     */
    public function pullModel(string $modelName): array
    {
        return $this->ollamaService->pullModel($modelName);
    }

    /**
     * Check if the required embedding model is available.
     */
    public function isEmbeddingModelAvailable(): bool
    {
        $requiredModel = $this->config['embedding_model'] ?? 'nomic-embed-text';
        $availableModels = $this->getAvailableModels();
        
        return in_array($requiredModel, $availableModels) || 
               collect($availableModels)->contains(fn($model) => str_starts_with($model, $requiredModel));
    }
}
