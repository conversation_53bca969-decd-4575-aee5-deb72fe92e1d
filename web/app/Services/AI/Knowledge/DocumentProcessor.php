<?php

namespace App\Services\AI\Knowledge;

use App\Models\AI\AIKnowledgeDocument;
use App\Models\AI\AIKnowledgeChunk;
use App\Services\AI\LLM\OpenAIService;
use App\Services\AI\VectorStore\PineconeService;
use App\Services\AI\Embedding\EmbeddingProviderFactory;
use App\Services\AI\Contracts\EmbeddingProviderInterface;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Exception;

class DocumentProcessor
{
    private OpenAIService $openAIService;
    private PineconeService $pineconeService;
    private EmbeddingProviderInterface $embeddingProvider;
    private array $config;

    public function __construct(
        OpenAIService $openAIService,
        PineconeService $pineconeService
    ) {
        $this->openAIService = $openAIService;
        $this->pineconeService = $pineconeService;
        $this->embeddingProvider = EmbeddingProviderFactory::create();
        $this->config = config('ai_assistant.knowledge_base');
    }

    /**
     * Process and store a document in the knowledge base.
     */
    public function processDocument(
        string $title,
        string $content,
        string $category,
        ?string $subcategory = null,
        array $tags = [],
        ?string $sourceFile = null
    ): ?AIKnowledgeDocument {
        try {
            DB::beginTransaction();

            Log::info('Processing document', [
                'title' => $title,
                'category' => $category,
                'subcategory' => $subcategory,
                'tags' => $tags,
                'source_file' => $sourceFile,
                'content_length' => strlen($content),
            ]);

            // Check if document already exists
            $contentHash = hash('sha256', $content);
            $existingDoc = AIKnowledgeDocument::where('content_hash', $contentHash)->first();

            if ($existingDoc) {
                Log::info('Document already exists', ['title' => $title]);
                DB::rollBack();
                return $existingDoc;
            }

            // Create document record
            $document = AIKnowledgeDocument::create([
                'title' => $title,
                'content' => $content,
                'category' => $category,
                'subcategory' => $subcategory,
                'tags' => $tags,
                'source_file' => $sourceFile,
                'content_hash' => $contentHash,
                'is_active' => true,
            ]);

            Log::info('Document created successfully', [
                'id' => $document->id,
                'title' => $title,
            ]);

            // Process document chunks
            $this->processDocumentChunks($document);

            DB::commit();

            Log::info('Successfully processed document', [
                'id' => $document->id,
                'title' => $title,
                'category' => $category,
                'chunks_count' => $document->chunks()->count(),
            ]);

            return $document;
        } catch (Exception $e) {
            DB::rollBack();

            Log::error('Failed to process document', [
                'title' => $title,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return null;
        }
    }

    /**
     * Process document into chunks and store embeddings.
     */
    private function processDocumentChunks(AIKnowledgeDocument $document): void
    {
        // Split document into chunks
        $chunks = $this->chunkDocument($document->content);

        // Generate embeddings for all chunks using the configured provider
        $embeddings = $this->embeddingProvider->generateEmbeddings($chunks);

        if (!$embeddings['success']) {
            Log::warning('Failed to generate embeddings, storing without vector search: ' . $embeddings['error']);
            // Create mock embeddings for now
            $dimension = $this->embeddingProvider->getEmbeddingDimension();
            $embeddings = [
                'success' => true,
                'embeddings' => array_fill(0, count($chunks), array_fill(0, $dimension, 0.0)),
                'usage' => ['total_tokens' => 0]
            ];
        }

        // Prepare vectors for Pinecone
        $vectors = [];
        $chunkRecords = [];

        foreach ($chunks as $index => $chunkContent) {
            $vectorId = $this->pineconeService->generateVectorId('chunk');

            // Prepare vector for Pinecone
            $vectors[] = $this->pineconeService->formatVector(
                $vectorId,
                $embeddings['embeddings'][$index],
                [
                    'document_id' => $document->id,
                    'chunk_index' => $index,
                    'category' => $document->category,
                    'subcategory' => $document->subcategory,
                    'title' => $document->title,
                    'tags' => $document->tags ?? [],
                ]
            );

            // Prepare chunk record for database
            $chunkRecords[] = [
                'document_id' => $document->id,
                'content' => $chunkContent,
                'chunk_index' => $index,
                'pinecone_vector_id' => $vectorId,
                'metadata' => json_encode([
                    'length' => strlen($chunkContent),
                    'word_count' => str_word_count($chunkContent),
                ]),
                'created_at' => now(),
                'updated_at' => now(),
            ];
        }

        // Store vectors in Pinecone (skip if not configured)
        // if (!empty(config('ai_assistant.pinecone.api_key'))) {
        //     if (!$this->pineconeService->upsertVectors($vectors)) {
        //         Log::warning('Failed to store vectors in Pinecone, continuing without vector storage');
        //     }
        // } else {
        //     Log::info('Pinecone not configured, skipping vector storage');
        // }


        // Store vectors in Pinecone (skip if not configured)
        if (!empty(config('ai_assistant.pinecone.api_key'))) {
            if (!$this->pineconeService->upsertVectors($vectors)) {
                Log::warning('Failed to store vectors in Pinecone, continuing without vector storage');
            }
        } else {
            Log::info('Pinecone not configured, skipping vector storage');
        }

        // Store chunk records in database
        AIKnowledgeChunk::insert($chunkRecords);

        // Update document with Pinecone metadata
        $document->update([
            'embedding_metadata' => [
                'chunks_count' => count($chunks),
                'total_tokens' => $embeddings['usage']['total_tokens'],
                'processed_at' => now()->toISOString(),
            ],
        ]);
    }

    /**
     * Split document content into chunks.
     */
    private function chunkDocument(string $content): array
    {
        $chunkSize = $this->config['chunk_size'];
        $overlap = $this->config['chunk_overlap'];

        // Clean and normalize content
        $content = $this->cleanContent($content);

        // Split by paragraphs first
        $paragraphs = preg_split('/\n\s*\n/', $content, -1, PREG_SPLIT_NO_EMPTY);

        $chunks = [];
        $currentChunk = '';

        foreach ($paragraphs as $paragraph) {
            $paragraph = trim($paragraph);

            // If paragraph is too long, split it further
            if (strlen($paragraph) > $chunkSize) {
                // Add current chunk if not empty
                if (!empty($currentChunk)) {
                    $chunks[] = trim($currentChunk);
                    $currentChunk = '';
                }

                // Split long paragraph by sentences
                $sentences = $this->splitIntoSentences($paragraph);
                $tempChunk = '';

                foreach ($sentences as $sentence) {
                    if (strlen($tempChunk . ' ' . $sentence) > $chunkSize) {
                        if (!empty($tempChunk)) {
                            $chunks[] = trim($tempChunk);
                        }
                        $tempChunk = $sentence;
                    } else {
                        $tempChunk .= empty($tempChunk) ? $sentence : ' ' . $sentence;
                    }
                }

                if (!empty($tempChunk)) {
                    $currentChunk = $tempChunk;
                }
            } else {
                // Check if adding this paragraph exceeds chunk size
                if (strlen($currentChunk . "\n\n" . $paragraph) > $chunkSize) {
                    if (!empty($currentChunk)) {
                        $chunks[] = trim($currentChunk);
                    }
                    $currentChunk = $paragraph;
                } else {
                    $currentChunk .= empty($currentChunk) ? $paragraph : "\n\n" . $paragraph;
                }
            }
        }

        // Add final chunk
        if (!empty($currentChunk)) {
            $chunks[] = trim($currentChunk);
        }

        // Apply overlap if configured
        if ($overlap > 0 && count($chunks) > 1) {
            $chunks = $this->applyOverlap($chunks, $overlap);
        }

        return array_filter($chunks, fn($chunk) => strlen(trim($chunk)) > 50);
    }

    /**
     * Clean and normalize document content.
     */
    private function cleanContent(string $content): string
    {
        // Remove excessive whitespace
        $content = preg_replace('/\s+/', ' ', $content);

        // Normalize line breaks
        $content = preg_replace('/\r\n|\r/', "\n", $content);

        // Remove multiple consecutive line breaks
        $content = preg_replace('/\n{3,}/', "\n\n", $content);

        return trim($content);
    }

    /**
     * Split text into sentences.
     */
    private function splitIntoSentences(string $text): array
    {
        // Simple sentence splitting - can be improved with NLP libraries
        $sentences = preg_split('/(?<=[.!?])\s+/', $text, -1, PREG_SPLIT_NO_EMPTY);
        return array_map('trim', $sentences);
    }

    /**
     * Apply overlap between chunks.
     */
    private function applyOverlap(array $chunks, int $overlapSize): array
    {
        $overlappedChunks = [];

        for ($i = 0; $i < count($chunks); $i++) {
            $chunk = $chunks[$i];

            // Add overlap from previous chunk
            if ($i > 0 && $overlapSize > 0) {
                $prevChunk = $chunks[$i - 1];
                $overlap = substr($prevChunk, -$overlapSize);
                $chunk = $overlap . ' ' . $chunk;
            }

            $overlappedChunks[] = $chunk;
        }

        return $overlappedChunks;
    }

    /**
     * Update existing document.
     */
    public function updateDocument(AIKnowledgeDocument $document, string $newContent): bool
    {
        try {
            DB::beginTransaction();

            // Delete existing chunks from Pinecone
            $vectorIds = $document->chunks()->pluck('pinecone_vector_id')->toArray();
            if (!empty($vectorIds)) {
                $this->pineconeService->deleteVectors($vectorIds);
            }

            // Delete existing chunks from database
            $document->chunks()->delete();

            // Update document content
            $document->updateContent($newContent);

            // Reprocess chunks
            $this->processDocumentChunks($document);

            DB::commit();

            Log::info('Successfully updated document', [
                'id' => $document->id,
                'title' => $document->title,
            ]);

            return true;
        } catch (Exception $e) {
            DB::rollBack();

            Log::error('Failed to update document', [
                'id' => $document->id,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * Delete document and its chunks.
     */
    public function deleteDocument(AIKnowledgeDocument $document): bool
    {
        try {
            DB::beginTransaction();

            // Delete chunks from Pinecone
            $vectorIds = $document->chunks()->pluck('pinecone_vector_id')->toArray();
            if (!empty($vectorIds)) {
                $this->pineconeService->deleteVectors($vectorIds);
            }

            // Delete from database
            $document->delete();

            DB::commit();

            Log::info('Successfully deleted document', [
                'id' => $document->id,
                'title' => $document->title,
            ]);

            return true;
        } catch (Exception $e) {
            DB::rollBack();

            Log::error('Failed to delete document', [
                'id' => $document->id,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * Regenerate embeddings for existing documents.
     */
    public function regenerateEmbeddings(?string $provider = null): array
    {
        $results = ['success' => 0, 'failed' => 0, 'errors' => []];

        try {
            // Switch to specified provider if provided
            if ($provider) {
                $this->embeddingProvider = EmbeddingProviderFactory::create($provider);
            }

            $documents = AIKnowledgeDocument::with('chunks')->get();

            foreach ($documents as $document) {
                try {
                    Log::info('Regenerating embeddings for document', [
                        'id' => $document->id,
                        'title' => $document->title,
                        'provider' => $this->embeddingProvider->getProviderName(),
                    ]);

                    // Get existing chunks
                    $chunks = $document->chunks()->orderBy('chunk_index')->pluck('content')->toArray();

                    if (empty($chunks)) {
                        Log::warning('No chunks found for document', ['id' => $document->id]);
                        continue;
                    }

                    // Generate new embeddings
                    $embeddings = $this->embeddingProvider->generateEmbeddings($chunks);

                    if (!$embeddings['success']) {
                        $results['failed']++;
                        $results['errors'][] = "Document {$document->id}: " . $embeddings['error'];
                        continue;
                    }

                    // Update Pinecone vectors
                    $vectors = [];
                    foreach ($document->chunks as $index => $chunk) {
                        $vectors[] = $this->pineconeService->formatVector(
                            $chunk->pinecone_vector_id,
                            $embeddings['embeddings'][$index],
                            [
                                'document_id' => $document->id,
                                'chunk_index' => $chunk->chunk_index,
                                'category' => $document->category,
                                'subcategory' => $document->subcategory,
                                'title' => $document->title,
                                'tags' => $document->tags ?? [],
                            ]
                        );
                    }

                    // Update vectors in Pinecone
                    if (!empty(config('ai_assistant.pinecone.api_key'))) {
                        $this->pineconeService->upsertVectors($vectors);
                    }

                    // Update document metadata
                    $document->update([
                        'embedding_metadata' => [
                            'chunks_count' => count($chunks),
                            'total_tokens' => $embeddings['usage']['total_tokens'],
                            'provider' => $this->embeddingProvider->getProviderName(),
                            'regenerated_at' => now()->toISOString(),
                        ],
                    ]);

                    $results['success']++;
                } catch (Exception $e) {
                    $results['failed']++;
                    $results['errors'][] = "Document {$document->id}: " . $e->getMessage();
                    Log::error('Failed to regenerate embeddings for document', [
                        'id' => $document->id,
                        'error' => $e->getMessage(),
                    ]);
                }
            }

            Log::info('Embedding regeneration completed', $results);
            return $results;
        } catch (Exception $e) {
            Log::error('Failed to regenerate embeddings', [
                'error' => $e->getMessage(),
            ]);
            return ['success' => 0, 'failed' => 0, 'errors' => [$e->getMessage()]];
        }
    }
}
