<?php

namespace App\Services\AI\LLM;

use Exception;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class OllamaService
{
    private array $config;
    private string $baseUrl;

    public function __construct()
    {
        $this->config = config('ai_assistant.ollama');
        $this->baseUrl = $this->config['base_url'] ?? 'http://localhost:11434';
    }

    /**
     * Generate embeddings using Ollama with nomic-embed-text model.
     */
    public function generateEmbeddings(array $texts): array
    {
        try {
            $embeddings = [];
            $totalTokens = 0;

            foreach ($texts as $text) {
                $response = Http::timeout($this->config['timeout'] ?? 60)
                    ->post("{$this->baseUrl}/api/embeddings", [
                        'model' => $this->config['embedding_model'] ?? 'nomic-embed-text',
                        'prompt' => $text,
                    ]);

                if (!$response->successful()) {
                    throw new Exception('Ollama API error: ' . $response->body());
                }

                $data = $response->json();

                if (!isset($data['embedding'])) {
                    throw new Exception('Invalid response from Ollama: missing embedding data');
                }

                $embedding = $data['embedding'];

                // Pad embedding to match Pinecone dimension if needed
                $targetDimension = config('ai_assistant.pinecone.dimension', 3072);
                if (count($embedding) < $targetDimension) {
                    $embedding = array_pad($embedding, $targetDimension, 0.0);
                }

                $embeddings[] = $embedding;
                $totalTokens += str_word_count($text); // Approximate token count
            }

            return [
                'success' => true,
                'embeddings' => $embeddings,
                'usage' => [
                    'total_tokens' => $totalTokens,
                    'prompt_tokens' => $totalTokens,
                ]
            ];
        } catch (Exception $e) {
            Log::error('Ollama embedding generation error', [
                'error' => $e->getMessage(),
                'texts_count' => count($texts),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'embeddings' => [],
                'usage' => ['total_tokens' => 0]
            ];
        }
    }

    /**
     * Generate a single embedding.
     */
    public function generateEmbedding(string $text): array
    {
        $result = $this->generateEmbeddings([$text]);

        if ($result['success'] && !empty($result['embeddings'])) {
            return [
                'success' => true,
                'embedding' => $result['embeddings'][0],
                'usage' => $result['usage']
            ];
        }

        return [
            'success' => false,
            'error' => $result['error'] ?? 'Failed to generate embedding',
            'embedding' => [],
            'usage' => ['total_tokens' => 0]
        ];
    }

    /**
     * Generate chat completion using Ollama.
     */
    public function generateChatCompletion(array $messages, array $options = []): array
    {
        try {
            $response = Http::timeout($this->config['timeout'] ?? 60)
                ->post("{$this->baseUrl}/api/chat", [
                    'model' => $this->config['chat_model'] ?? 'llama3.2',
                    'messages' => $messages,
                    'stream' => false,
                    'options' => array_merge([
                        'temperature' => (float) ($this->config['temperature'] ?? 0.7),
                        'top_p' => (float) ($this->config['top_p'] ?? 0.9),
                    ], $options)
                ]);

            if (!$response->successful()) {
                throw new Exception('Ollama API error: ' . $response->body());
            }

            $data = $response->json();

            if (!isset($data['message']['content'])) {
                throw new Exception('Invalid response from Ollama: missing message content');
            }

            return [
                'success' => true,
                'content' => $data['message']['content'],
                'usage' => [
                    'prompt_tokens' => $data['prompt_eval_count'] ?? 0,
                    'completion_tokens' => $data['eval_count'] ?? 0,
                    'total_tokens' => ($data['prompt_eval_count'] ?? 0) + ($data['eval_count'] ?? 0),
                ],
                'model' => $data['model'] ?? $this->config['chat_model'],
            ];
        } catch (Exception $e) {
            Log::error('Ollama chat completion error', [
                'error' => $e->getMessage(),
                'messages_count' => count($messages),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'content' => '',
                'usage' => ['total_tokens' => 0]
            ];
        }
    }

    /**
     * Check if Ollama is available and the required models are installed.
     */
    public function validateConfiguration(): bool
    {
        try {
            // Check if Ollama is running
            $response = Http::timeout(5)->get("{$this->baseUrl}/api/tags");

            if (!$response->successful()) {
                return false;
            }

            $data = $response->json();
            $availableModels = collect($data['models'] ?? [])->pluck('name')->toArray();

            // Check if required models are available
            $embeddingModel = $this->config['embedding_model'] ?? 'nomic-embed-text';
            $chatModel = $this->config['chat_model'] ?? 'llama3.2';

            $hasEmbeddingModel = in_array($embeddingModel, $availableModels) ||
                collect($availableModels)->contains(fn($model) => str_starts_with($model, $embeddingModel));

            $hasChatModel = in_array($chatModel, $availableModels) ||
                collect($availableModels)->contains(fn($model) => str_starts_with($model, $chatModel));

            Log::info('Ollama model availability check', [
                'available_models' => $availableModels,
                'embedding_model' => $embeddingModel,
                'chat_model' => $chatModel,
                'has_embedding_model' => $hasEmbeddingModel,
                'has_chat_model' => $hasChatModel,
            ]);

            return $hasEmbeddingModel; // At minimum, we need embedding model
        } catch (Exception $e) {
            Log::error('Ollama configuration validation failed', [
                'error' => $e->getMessage(),
                'base_url' => $this->baseUrl,
            ]);
            return false;
        }
    }

    /**
     * Get available models from Ollama.
     */
    public function getAvailableModels(): array
    {
        try {
            $response = Http::timeout(10)->get("{$this->baseUrl}/api/tags");

            if (!$response->successful()) {
                return [];
            }

            $data = $response->json();
            return collect($data['models'] ?? [])->pluck('name')->toArray();
        } catch (Exception $e) {
            Log::error('Failed to get Ollama models', [
                'error' => $e->getMessage(),
            ]);
            return [];
        }
    }

    /**
     * Pull a model from Ollama registry.
     */
    public function pullModel(string $modelName): array
    {
        try {
            $response = Http::timeout(300) // 5 minutes for model download
                ->post("{$this->baseUrl}/api/pull", [
                    'name' => $modelName,
                ]);

            if (!$response->successful()) {
                throw new Exception('Failed to pull model: ' . $response->body());
            }

            return [
                'success' => true,
                'message' => "Model {$modelName} pulled successfully"
            ];
        } catch (Exception $e) {
            Log::error('Failed to pull Ollama model', [
                'model' => $modelName,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
}
