<?php

namespace App\Services\AI\LLM;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Exception;

class OpenAIService
{
    private array $config;

    public function __construct()
    {
        $this->config = config('ai_assistant.openai');
    }

    /**
     * Generate chat completion with function calling support.
     */
    public function chat(array $messages, array $functions = [], array $options = []): array
    {
        try {
            $payload = [
                'model' => $options['model'] ?? $this->config['model'],
                'messages' => $messages,
                'max_tokens' => $options['max_tokens'] ?? $this->config['max_tokens'],
                'temperature' => $options['temperature'] ?? $this->config['temperature'],
            ];

            // Add function calling if functions are provided
            if (!empty($functions)) {
                $payload['functions'] = $functions;
                $payload['function_call'] = 'auto';
            }

            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->config['api_key'],
                'Content-Type' => 'application/json',
            ])->timeout($this->config['timeout'])
                ->post('https://api.openai.com/v1/chat/completions', $payload);

            if ($response->successful()) {
                $data = $response->json();

                return [
                    'success' => true,
                    'content' => $data['choices'][0]['message']['content'] ?? '',
                    'function_call' => $data['choices'][0]['message']['function_call'] ?? null,
                    'usage' => [
                        'prompt_tokens' => $data['usage']['prompt_tokens'],
                        'completion_tokens' => $data['usage']['completion_tokens'],
                        'total_tokens' => $data['usage']['total_tokens'],
                    ],
                    'finish_reason' => $data['choices'][0]['finish_reason'],
                ];
            } else {
                throw new Exception('OpenAI API error: ' . $response->body());
            }
        } catch (Exception $e) {
            Log::error('OpenAI chat completion error', [
                'error' => $e->getMessage(),
                'messages_count' => count($messages),
                'functions_count' => count($functions),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Generate embeddings for text.
     */
    public function generateEmbedding(string $text): array
    {
        try {
            $response = OpenAI::embeddings()->create([
                'model' => $this->config['embedding_model'],
                'input' => $text,
            ]);

            return [
                'success' => true,
                'embedding' => $response->embeddings[0]->embedding,
                'usage' => [
                    'prompt_tokens' => $response->usage->promptTokens,
                    'total_tokens' => $response->usage->totalTokens,
                ],
            ];
        } catch (Exception $e) {
            Log::error('OpenAI embedding generation error', [
                'error' => $e->getMessage(),
                'text_length' => strlen($text),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Generate embeddings for multiple texts.
     */
    public function generateEmbeddings(array $texts): array
    {
        try {
            $response = OpenAI::embeddings()->create([
                'model' => $this->config['embedding_model'],
                'input' => $texts,
            ]);

            $embeddings = [];
            foreach ($response->embeddings as $embedding) {
                $embeddings[] = $embedding->embedding;
            }

            return [
                'success' => true,
                'embeddings' => $embeddings,
                'usage' => [
                    'prompt_tokens' => $response->usage->promptTokens,
                    'total_tokens' => $response->usage->totalTokens,
                ],
            ];
        } catch (Exception $e) {
            Log::error('OpenAI batch embedding generation error', [
                'error' => $e->getMessage(),
                'texts_count' => count($texts),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Validate API configuration.
     */
    public function validateConfiguration(): bool
    {
        try {
            $response = OpenAI::models()->list();
            return !empty($response->data);
        } catch (Exception $e) {
            Log::error('OpenAI configuration validation failed', [
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * Get available models.
     */
    public function getAvailableModels(): array
    {
        try {
            $response = OpenAI::models()->list();
            return collect($response->data)
                ->pluck('id')
                ->filter(fn($model) => str_contains($model, 'gpt'))
                ->values()
                ->toArray();
        } catch (Exception $e) {
            Log::error('Failed to fetch OpenAI models', [
                'error' => $e->getMessage(),
            ]);
            return [];
        }
    }

    /**
     * Estimate token count for text.
     */
    public function estimateTokenCount(string $text): int
    {
        // Rough estimation: 1 token ≈ 4 characters for English text
        return (int) ceil(strlen($text) / 4);
    }

    /**
     * Check if text exceeds token limit.
     */
    public function exceedsTokenLimit(string $text, int $limit = null): bool
    {
        $limit = $limit ?? $this->config['max_tokens'];
        return $this->estimateTokenCount($text) > $limit;
    }
}
