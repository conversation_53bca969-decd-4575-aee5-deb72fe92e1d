<?php

namespace App\Models\AI;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class AIKnowledgeDocument extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'content',
        'category',
        'subcategory',
        'tags',
        'pinecone_id',
        'embedding_metadata',
        'source_file',
        'content_hash',
        'is_active',
    ];

    protected $casts = [
        'tags' => 'array',
        'embedding_metadata' => 'array',
        'is_active' => 'boolean',
    ];

    /**
     * Get all chunks for this document.
     */
    public function chunks(): HasMany
    {
        return $this->hasMany(AIKnowledgeChunk::class, 'document_id');
    }

    /**
     * Generate content hash for duplicate detection.
     */
    public function generateContentHash(): string
    {
        return hash('sha256', $this->content);
    }

    /**
     * Check if document content has changed.
     */
    public function hasContentChanged(string $newContent): bool
    {
        return $this->content_hash !== hash('sha256', $newContent);
    }

    /**
     * Update content and regenerate hash.
     */
    public function updateContent(string $newContent): void
    {
        $this->update([
            'content' => $newContent,
            'content_hash' => hash('sha256', $newContent),
        ]);
    }

    /**
     * Get document tags as array.
     */
    public function getTagsArray(): array
    {
        return $this->tags ?? [];
    }

    /**
     * Add tag to document.
     */
    public function addTag(string $tag): void
    {
        $tags = $this->getTagsArray();
        if (!in_array($tag, $tags)) {
            $tags[] = $tag;
            $this->update(['tags' => $tags]);
        }
    }

    /**
     * Remove tag from document.
     */
    public function removeTag(string $tag): void
    {
        $tags = $this->getTagsArray();
        $tags = array_filter($tags, fn($t) => $t !== $tag);
        $this->update(['tags' => array_values($tags)]);
    }

    /**
     * Scope for active documents.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for documents by category.
     */
    public function scopeByCategory($query, string $category)
    {
        return $query->where('category', $category);
    }

    /**
     * Scope for documents by subcategory.
     */
    public function scopeBySubcategory($query, string $subcategory)
    {
        return $query->where('subcategory', $subcategory);
    }

    /**
     * Scope for documents with tags.
     */
    public function scopeWithTag($query, string $tag)
    {
        return $query->whereJsonContains('tags', $tag);
    }

    /**
     * Get category display name.
     */
    public function getCategoryDisplayName(): string
    {
        return match($this->category) {
            'app_domain' => 'App Domain',
            'shopify_platform' => 'Shopify Platform',
            'ebay_platform' => 'eBay Platform',
            'troubleshooting' => 'Troubleshooting',
            default => ucfirst($this->category),
        };
    }

    /**
     * Get subcategory display name.
     */
    public function getSubcategoryDisplayName(): string
    {
        if (!$this->subcategory) {
            return '';
        }

        return ucwords(str_replace('_', ' ', $this->subcategory));
    }
}
