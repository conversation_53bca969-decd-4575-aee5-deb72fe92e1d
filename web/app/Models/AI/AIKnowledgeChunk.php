<?php

namespace App\Models\AI;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class AIKnowledgeChunk extends Model
{
    use HasFactory;

    protected $fillable = [
        'document_id',
        'content',
        'chunk_index',
        'pinecone_vector_id',
        'metadata',
    ];

    protected $casts = [
        'metadata' => 'array',
    ];

    /**
     * Get the document that owns the chunk.
     */
    public function document(): BelongsTo
    {
        return $this->belongsTo(AIKnowledgeDocument::class, 'document_id');
    }

    /**
     * Get chunk metadata value.
     */
    public function getMetadata(string $key, $default = null)
    {
        return data_get($this->metadata, $key, $default);
    }

    /**
     * Set chunk metadata value.
     */
    public function setMetadata(string $key, $value): void
    {
        $metadata = $this->metadata ?? [];
        data_set($metadata, $key, $value);
        $this->update(['metadata' => $metadata]);
    }

    /**
     * Get chunk content preview.
     */
    public function getContentPreview(int $length = 100): string
    {
        return strlen($this->content) > $length 
            ? substr($this->content, 0, $length) . '...'
            : $this->content;
    }

    /**
     * Scope for chunks by document.
     */
    public function scopeByDocument($query, int $documentId)
    {
        return $query->where('document_id', $documentId);
    }

    /**
     * Scope for chunks ordered by index.
     */
    public function scopeOrderedByIndex($query)
    {
        return $query->orderBy('chunk_index');
    }
}
