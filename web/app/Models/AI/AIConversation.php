<?php

namespace App\Models\AI;

use App\Models\Ebay\EbayUser;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class AIConversation extends Model
{
    use HasFactory;

    protected $fillable = [
        'session_id',
        'ebay_user_id',
        'title',
        'context',
        'status',
        'last_activity_at',
    ];

    protected $casts = [
        'context' => 'array',
        'last_activity_at' => 'datetime',
    ];

    /**
     * Get the eBay user that owns the conversation.
     */
    public function ebayUser(): BelongsTo
    {
        return $this->belongsTo(EbayUser::class);
    }

    /**
     * Get all messages for this conversation.
     */
    public function messages(): HasMany
    {
        return $this->hasMany(AIMessage::class, 'conversation_id');
    }

    /**
     * Get all task executions for this conversation.
     */
    public function taskExecutions(): Has<PERSON>any
    {
        return $this->hasMany(AITaskExecution::class, 'conversation_id');
    }

    /**
     * Get recent messages for context.
     */
    public function getRecentMessages(int $limit = 10)
    {
        return $this->messages()
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get()
            ->reverse()
            ->values();
    }

    /**
     * Update last activity timestamp.
     */
    public function updateActivity(): void
    {
        $this->update(['last_activity_at' => now()]);
    }

    /**
     * Archive the conversation.
     */
    public function archive(): void
    {
        $this->update(['status' => 'archived']);
    }

    /**
     * Check if conversation is active.
     */
    public function isActive(): bool
    {
        return $this->status === 'active';
    }

    /**
     * Get conversation context with user and app data.
     */
    public function getFullContext(): array
    {
        $context = $this->context ?? [];
        
        if ($this->ebayUser) {
            $context['ebay_user'] = [
                'id' => $this->ebayUser->id,
                'ebay_user_name' => $this->ebayUser->ebay_user_name,
                'site_id' => $this->ebayUser->site_id,
                'has_active_profiles' => $this->ebayUser->profiles()->count() > 0,
            ];
        }

        return $context;
    }

    /**
     * Scope for active conversations.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope for conversations by session.
     */
    public function scopeBySession($query, string $sessionId)
    {
        return $query->where('session_id', $sessionId);
    }
}
