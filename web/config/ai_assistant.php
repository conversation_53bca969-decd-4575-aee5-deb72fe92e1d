<?php

return [
    /*
    |--------------------------------------------------------------------------
    | OpenAI Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for OpenAI API integration including model settings,
    | token limits, and API parameters for the AI assistant.
    |
    */
    'openai' => [
        'api_key' => env('OPENAI_API_KEY'),
        'organization' => env('OPENAI_ORGANIZATION'),
        'model' => env('OPENAI_MODEL', 'gpt-4-turbo-preview'),
        'embedding_model' => env('OPENAI_EMBEDDING_MODEL', 'text-embedding-ada-002'),
        'max_tokens' => env('OPENAI_MAX_TOKENS', 4000),
        'temperature' => env('OPENAI_TEMPERATURE', 0.7),
        'timeout' => env('OPENAI_TIMEOUT', 60),
    ],

    /*
    |--------------------------------------------------------------------------
    | Pinecone Vector Database Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for Pinecone vector database used for storing and
    | retrieving document embeddings for the RAG pipeline.
    |
    */
    'pinecone' => [
        'api_key' => env('PINECONE_API_KEY'),
        'environment' => env('PINECONE_ENVIRONMENT'),
        'index_name' => env('PINECONE_INDEX_NAME', 'ebay-shopify-knowledge'),
        'dimension' => 1536, // OpenAI embedding dimension
        'metric' => 'cosine',
        'timeout' => env('PINECONE_TIMEOUT', 30),
    ],

    /*
    |--------------------------------------------------------------------------
    | Knowledge Base Configuration
    |--------------------------------------------------------------------------
    |
    | Settings for document processing, chunking, and knowledge base
    | management for the AI assistant.
    |
    */
    'knowledge_base' => [
        'chunk_size' => env('AI_CHUNK_SIZE', 1000),
        'chunk_overlap' => env('AI_CHUNK_OVERLAP', 200),
        'max_chunks_per_document' => env('AI_MAX_CHUNKS_PER_DOCUMENT', 50),
        'similarity_threshold' => env('AI_SIMILARITY_THRESHOLD', 0.7),
        'max_retrieved_chunks' => env('AI_MAX_RETRIEVED_CHUNKS', 10),
    ],

    /*
    |--------------------------------------------------------------------------
    | Conversation Configuration
    |--------------------------------------------------------------------------
    |
    | Settings for managing AI conversations, context, and session handling.
    |
    */
    'conversation' => [
        'max_messages_per_conversation' => env('AI_MAX_MESSAGES', 50),
        'context_window_size' => env('AI_CONTEXT_WINDOW', 10),
        'conversation_timeout_hours' => env('AI_CONVERSATION_TIMEOUT', 24),
        'auto_archive_days' => env('AI_AUTO_ARCHIVE_DAYS', 30),
    ],

    /*
    |--------------------------------------------------------------------------
    | Task Execution Configuration
    |--------------------------------------------------------------------------
    |
    | Settings for AI task automation and function calling capabilities.
    |
    */
    'tasks' => [
        'enabled' => env('AI_TASKS_ENABLED', true),
        'timeout_seconds' => env('AI_TASK_TIMEOUT', 300),
        'max_concurrent_tasks' => env('AI_MAX_CONCURRENT_TASKS', 5),
        'allowed_functions' => [
            'create_ebay_profile',
            'get_recommended_categories',
            'analyze_sync_issues',
            'update_sync_settings',
            'get_product_insights',
            'troubleshoot_errors',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Rate Limiting Configuration
    |--------------------------------------------------------------------------
    |
    | Rate limiting settings to prevent API abuse and manage costs.
    |
    */
    'rate_limiting' => [
        'requests_per_minute' => env('AI_REQUESTS_PER_MINUTE', 60),
        'requests_per_hour' => env('AI_REQUESTS_PER_HOUR', 1000),
        'tokens_per_day' => env('AI_TOKENS_PER_DAY', 100000),
    ],

    /*
    |--------------------------------------------------------------------------
    | Logging Configuration
    |--------------------------------------------------------------------------
    |
    | Settings for AI assistant logging and monitoring.
    |
    */
    'logging' => [
        'enabled' => env('AI_LOGGING_ENABLED', true),
        'log_channel' => env('AI_LOG_CHANNEL', 'ai_assistant'),
        'log_level' => env('AI_LOG_LEVEL', 'info'),
        'log_conversations' => env('AI_LOG_CONVERSATIONS', true),
        'log_embeddings' => env('AI_LOG_EMBEDDINGS', false),
    ],

    /*
    |--------------------------------------------------------------------------
    | Knowledge Categories
    |--------------------------------------------------------------------------
    |
    | Predefined categories for organizing knowledge base documents.
    |
    */
    'knowledge_categories' => [
        'app_domain' => [
            'profile_management',
            'sync_operations',
            'integration_workflows',
            'user_guides',
        ],
        'shopify_platform' => [
            'product_management',
            'inventory_tracking',
            'order_processing',
            'api_integration',
            'webhooks',
        ],
        'ebay_platform' => [
            'category_hierarchy',
            'listing_requirements',
            'item_specifics',
            'policies',
            'fees_structure',
        ],
        'troubleshooting' => [
            'sync_issues',
            'authentication_problems',
            'api_errors',
            'configuration_issues',
            'performance_optimization',
        ],
    ],
];
