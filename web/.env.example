APP_NAME="eBay Integration & Sync"
APP_ENV=local
APP_KEY=
APP_DEBUG=true

LOG_CHANNEL=stack
LOG_LEVEL=debug

DB_CONNECTION=sqlite
# The path to the database file should be absolute, make sure to update it!
DB_DATABASE=storage/db.sqlite
DB_FOREIGN_KEYS=true

ADMIN_APP_KEY=
HELPSCOUT_APP_ID=
HELPSCOUT_APP_SECRET=

DB_CONNECTION=
DB_DATABASE=
DB_USERNAME=
DB_PASSWORD=

QUEUE_CONNECTION=

EBAY_RU_VALUE=
EBAY_CLIENT_ID=
EBAY_CLIENT_SECRET=
EBAY_OAUTH_ENDPOINT=
EBAY_DEV_ID=
EBAY_IS_SANDBOX=


BROADCAST_DRIVER=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_APP_ID=
PUSHER_APP_CLUSTER=

MONGO_DB_URI=


AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=
AWS_ACCOUNT_ID=

SQS_PREFIX="https://sqs.${AWS_DEFAULT_REGION}.amazonaws.com/${AWS_ACCOUNT_ID}"
EVENT_SOURCE_ARN=

CUSTOMER_SUPPORT_EMAIL_ADDRESS=

ORDER_SYNC_FAILED_MAIL_INTERVAL_IN_MINUTES=

# AI Assistant Configuration
OPENAI_API_KEY=
OPENAI_ORGANIZATION=
OPENAI_MODEL=gpt-4-turbo-preview
OPENAI_EMBEDDING_MODEL=text-embedding-ada-002
OPENAI_MAX_TOKENS=4000
OPENAI_TEMPERATURE=0.7

PINECONE_API_KEY=pcsk_NaxkC_9ttDF4B8iuitguY57X2WnDBR1BtT9zX2o6pca8WAY9WfyXqf5THkBf2CJtczcVB
PINECONE_ENVIRONMENT=
PINECONE_INDEX_NAME=ebay-shopify-knowledge

AI_CHUNK_SIZE=1000
AI_CHUNK_OVERLAP=200
AI_MAX_RETRIEVED_CHUNKS=10
AI_SIMILARITY_THRESHOLD=0.7

AI_TASKS_ENABLED=true
AI_REQUESTS_PER_MINUTE=60
AI_LOGGING_ENABLED=true
