APP_NAME="eBay Integration & Sync"
APP_ENV=local
APP_KEY=
APP_DEBUG=true

LOG_CHANNEL=stack
LOG_LEVEL=debug

DB_CONNECTION=sqlite
# The path to the database file should be absolute, make sure to update it!
DB_DATABASE=storage/db.sqlite
DB_FOREIGN_KEYS=true

ADMIN_APP_KEY=
HELPSCOUT_APP_ID=
HELPSCOUT_APP_SECRET=

DB_CONNECTION=
DB_DATABASE=
DB_USERNAME=
DB_PASSWORD=

QUEUE_CONNECTION=

EBAY_RU_VALUE=
EBAY_CLIENT_ID=
EBAY_CLIENT_SECRET=
EBAY_OAUTH_ENDPOINT=
EBAY_DEV_ID=
EBAY_IS_SANDBOX=


BROADCAST_DRIVER=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_APP_ID=
PUSHER_APP_CLUSTER=

MONGO_DB_URI=


AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=
AWS_ACCOUNT_ID=

SQS_PREFIX="https://sqs.${AWS_DEFAULT_REGION}.amazonaws.com/${AWS_ACCOUNT_ID}"
EVENT_SOURCE_ARN=

CUSTOMER_SUPPORT_EMAIL_ADDRESS=

ORDER_SYNC_FAILED_MAIL_INTERVAL_IN_MINUTES=

# AI Assistant Configuration
OPENAI_API_KEY=
OPENAI_ORGANIZATION=
OPENAI_MODEL=gpt-4o-mini
OPENAI_EMBEDDING_MODEL=text-embedding-ada-002
OPENAI_MAX_TOKENS=4000
OPENAI_TEMPERATURE=0.7

# Pinecone Configuration
PINECONE_API_KEY=
PINECONE_HOST=https://shopify-ebay-dpl-8kz58od.svc.aped-4627-b74a.pinecone.io
PINECONE_ENVIRONMENT=
PINECONE_INDEX_NAME=shopify-ebay-dpl
PINECONE_DIMENSION=3072

# Ollama Configuration (Local LLM/Embedding Service)
OLLAMA_BASE_URL=http://localhost:11434
OLLAMA_EMBEDDING_MODEL=nomic-embed-text
OLLAMA_CHAT_MODEL=llama3.2
OLLAMA_TIMEOUT=60
OLLAMA_TEMPERATURE=0.7
OLLAMA_TOP_P=0.9
OLLAMA_EMBEDDING_DIMENSION=768

# Embedding Provider Selection
# Choose which embedding provider to use: 'openai' or 'ollama'
AI_EMBEDDING_PROVIDER=ollama

AI_CHUNK_SIZE=1000
AI_CHUNK_OVERLAP=200
AI_MAX_RETRIEVED_CHUNKS=10
AI_SIMILARITY_THRESHOLD=0.7

AI_TASKS_ENABLED=true
AI_REQUESTS_PER_MINUTE=60
AI_LOGGING_ENABLED=true
